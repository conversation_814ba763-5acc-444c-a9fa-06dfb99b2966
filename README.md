# 大乐透数据分析器

一个基于Python和tkinter的大乐透历史数据分析GUI应用程序，可以分析大乐透开奖数据的各种统计模式。

## 功能特性

### 数据分析功能
- **奇偶比分析**：计算红球中奇数与偶数的比例
- **奇偶排布分析**：显示红球奇偶数的具体排列模式
- **分区比分析**：将1-35号码分为7个区间，统计各区间分布
- **历史模式匹配**：查找上一次出现相同模式的日期和间隔天数
- **0分区特殊分析**：专门分析分区比中为0的区间模式

### 界面功能
- 直观的表格显示所有分析结果
- 支持CSV文件导入
- 自动检测当前目录的数据文件
- 可滚动的数据表格
- 实时状态显示

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install pandas numpy
```

或者使用requirements.txt：
```bash
pip install -r requirements.txt
```

## 数据格式要求

CSV文件应包含以下列（按顺序）：
- 期号：大乐透期号
- 开奖日期：格式为YYYY-MM-DD
- 红球1-5：五个红球号码
- 蓝球1-2：两个蓝球号码

示例数据格式：
```csv
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
7001,2007-05-30,22,24,29,31,35,04,11
7002,2007-06-02,15,22,31,34,35,05,12
```

## 使用方法

### 1. 启动应用程序
```bash
python dlt_analyzer.py
```

### 2. 导入数据
- 点击"导入数据"按钮选择CSV文件
- 或者将数据文件命名为`dlt_data.csv`并放在程序同目录下（程序会自动检测）

### 3. 开始分析
- 点击"开始分析"按钮进行数据分析
- 等待分析完成，结果将显示在表格中

### 4. 查看结果
表格包含以下列：
- **日期**：开奖日期
- **期号**：大乐透期号
- **红球**：五个红球号码
- **蓝球**：两个蓝球号码
- **奇偶比**：红球奇偶数比例（如3:2）
- **奇偶排布**：奇偶数排列模式（如奇偶奇偶奇）
- **上次奇偶排布**：上一次出现相同奇偶排布的日期和间隔
- **分区比**：7个区间的号码分布（如1:2:1:1:0:0:0）
- **上次分区比**：上一次出现相同分区比的日期和间隔
- **上次0分区比**：上一次出现相同0分区模式的日期和间隔

## 分区说明

红球号码1-35被分为7个区间：
- 区间1：1-5
- 区间2：6-10
- 区间3：11-15
- 区间4：16-20
- 区间5：21-25
- 区间6：26-30
- 区间7：31-35

## 文件结构

```
大乐透预测0731/
├── dlt_analyzer.py      # 主程序文件
├── dlt_data.csv         # 示例数据文件
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

## 注意事项

1. 确保数据文件格式正确，列名必须与要求一致
2. 程序会按日期排序数据，确保日期格式正确
3. 分析大量数据时可能需要一些时间，请耐心等待
4. 表格默认按最新日期在前的顺序显示

## 故障排除

### 常见问题
1. **导入失败**：检查CSV文件格式和编码（建议使用UTF-8）
2. **分析错误**：确保数据中没有缺失值或格式错误
3. **界面显示问题**：调整窗口大小或使用滚动条

### 技术支持
如遇到问题，请检查：
- Python版本是否符合要求
- 依赖包是否正确安装
- 数据文件格式是否正确