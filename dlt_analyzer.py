#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透数据分析应用程序
功能：分析大乐透历史数据，计算奇偶比、分区比等统计信息
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
from datetime import datetime
from collections import Counter
import os

class DaLeTouAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("大乐透数据分析器")
        self.root.geometry("1400x800")

        # 数据存储
        self.df = None
        self.analyzed_data = None

        # 创建界面
        self.create_widgets()

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 导入数据按钮
        self.import_btn = ttk.Button(
            button_frame,
            text="导入数据",
            command=self.import_data
        )
        self.import_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 分析按钮
        self.analyze_btn = ttk.Button(
            button_frame,
            text="开始分析",
            command=self.analyze_data,
            state=tk.DISABLED
        )
        self.analyze_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 状态标签
        self.status_label = ttk.Label(button_frame, text="请导入数据文件")
        self.status_label.pack(side=tk.LEFT, padx=(10, 0))

        # 创建表格
        self.create_table(main_frame)

    def create_table(self, parent):
        """创建数据表格"""
        # 表格框架
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # 定义列
        columns = (
            "日期", "期号", "红球", "蓝球", "奇偶比", "奇偶排布",
            "上次奇偶排布", "分区比", "上次分区比", "上次0分区比"
        )

        # 创建Treeview
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # 设置列标题和宽度
        column_widths = {
            "日期": 100, "期号": 80, "红球": 120, "蓝球": 80, "奇偶比": 80,
            "奇偶排布": 100, "上次奇偶排布": 150, "分区比": 120,
            "上次分区比": 150, "上次0分区比": 150
        }

        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

    def import_data(self):
        """导入数据文件"""
        try:
            # 检查当前目录是否有dlt_data.csv
            if os.path.exists("dlt_data.csv"):
                file_path = "dlt_data.csv"
                self.status_label.config(text="使用当前目录的dlt_data.csv")
            else:
                file_path = filedialog.askopenfilename(
                    title="选择大乐透数据文件",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
                )
                if not file_path:
                    return

            # 读取数据
            self.df = pd.read_csv(file_path, encoding='utf-8')

            # 验证数据格式
            required_columns = ['期号', '开奖日期', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
            if not all(col in self.df.columns for col in required_columns):
                messagebox.showerror("错误", "数据文件格式不正确，请检查列名")
                return

            # 数据预处理
            self.preprocess_data()

            self.status_label.config(text=f"已导入 {len(self.df)} 条记录")
            self.analyze_btn.config(state=tk.NORMAL)

        except Exception as e:
            messagebox.showerror("错误", f"导入数据失败：{str(e)}")

    def preprocess_data(self):
        """数据预处理"""
        # 转换日期格式
        self.df['开奖日期'] = pd.to_datetime(self.df['开奖日期'])

        # 按日期排序
        self.df = self.df.sort_values('开奖日期').reset_index(drop=True)

        # 创建红球和蓝球列表
        self.df['红球列表'] = self.df[['红球1', '红球2', '红球3', '红球4', '红球5']].values.tolist()
        self.df['蓝球列表'] = self.df[['蓝球1', '蓝球2']].values.tolist()

    def calculate_odd_even_ratio(self, red_balls):
        """计算奇偶比"""
        odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
        even_count = 5 - odd_count
        return f"{odd_count}:{even_count}"

    def calculate_odd_even_pattern(self, red_balls):
        """计算奇偶排布"""
        pattern = ""
        for ball in red_balls:
            pattern += "奇" if ball % 2 == 1 else "偶"
        return pattern

    def calculate_zone_ratio(self, red_balls):
        """计算分区比（1-35分为7个区间）"""
        zones = [0] * 7  # 7个区间

        for ball in red_balls:
            zone_index = min((ball - 1) // 5, 6)  # 每个区间5个号码，最后一个区间包含35
            zones[zone_index] += 1

        return ":".join(map(str, zones))

    def find_previous_pattern(self, current_index, pattern_type, current_pattern):
        """查找上一次出现相同模式的记录"""
        if current_index == 0:
            return "首次出现", 0

        for i in range(current_index - 1, -1, -1):
            if self.analyzed_data.iloc[i][pattern_type] == current_pattern:
                prev_date = self.analyzed_data.iloc[i]['开奖日期']
                current_date = self.analyzed_data.iloc[current_index]['开奖日期']
                days_diff = (current_date - prev_date).days
                return f"{prev_date.strftime('%Y-%m-%d')} ({days_diff}天)", days_diff

        return "未找到", 0

    def find_zero_zone_pattern(self, current_index, current_zone_ratio):
        """查找上一次出现相同0分区模式的记录"""
        if current_index == 0:
            return "首次出现", 0

        # 解析当前分区比，找出为0的位置
        current_zones = list(map(int, current_zone_ratio.split(':')))
        current_zero_positions = [i for i, zone in enumerate(current_zones) if zone == 0]

        for i in range(current_index - 1, -1, -1):
            prev_zone_ratio = self.analyzed_data.iloc[i]['分区比']
            prev_zones = list(map(int, prev_zone_ratio.split(':')))
            prev_zero_positions = [j for j, zone in enumerate(prev_zones) if zone == 0]

            # 比较0分区的数量和位置是否相同
            if (len(current_zero_positions) == len(prev_zero_positions) and
                current_zero_positions == prev_zero_positions):
                prev_date = self.analyzed_data.iloc[i]['开奖日期']
                current_date = self.analyzed_data.iloc[current_index]['开奖日期']
                days_diff = (current_date - prev_date).days
                return f"{prev_date.strftime('%Y-%m-%d')} ({days_diff}天)", days_diff

        return "未找到", 0

    def analyze_data(self):
        """分析数据"""
        if self.df is None:
            messagebox.showerror("错误", "请先导入数据")
            return

        try:
            self.status_label.config(text="正在分析数据...")
            self.root.update()

            # 创建分析结果DataFrame
            analysis_results = []

            for index, row in self.df.iterrows():
                red_balls = row['红球列表']
                blue_balls = row['蓝球列表']

                # 基本信息
                result = {
                    '开奖日期': row['开奖日期'],
                    '期号': row['期号'],
                    '红球': f"{red_balls[0]:02d}-{red_balls[1]:02d}-{red_balls[2]:02d}-{red_balls[3]:02d}-{red_balls[4]:02d}",
                    '蓝球': f"{blue_balls[0]:02d}-{blue_balls[1]:02d}",
                    '奇偶比': self.calculate_odd_even_ratio(red_balls),
                    '奇偶排布': self.calculate_odd_even_pattern(red_balls),
                    '分区比': self.calculate_zone_ratio(red_balls)
                }

                analysis_results.append(result)

            # 转换为DataFrame
            self.analyzed_data = pd.DataFrame(analysis_results)

            # 计算历史匹配信息
            for index in range(len(self.analyzed_data)):
                # 上次奇偶排布
                odd_even_pattern = self.analyzed_data.iloc[index]['奇偶排布']
                prev_odd_even, _ = self.find_previous_pattern(index, '奇偶排布', odd_even_pattern)
                self.analyzed_data.at[index, '上次奇偶排布'] = prev_odd_even

                # 上次分区比
                zone_ratio = self.analyzed_data.iloc[index]['分区比']
                prev_zone_ratio, _ = self.find_previous_pattern(index, '分区比', zone_ratio)
                self.analyzed_data.at[index, '上次分区比'] = prev_zone_ratio

                # 上次0分区比
                prev_zero_zone, _ = self.find_zero_zone_pattern(index, zone_ratio)
                self.analyzed_data.at[index, '上次0分区比'] = prev_zero_zone

            # 更新表格显示
            self.update_table()

            self.status_label.config(text=f"分析完成，共 {len(self.analyzed_data)} 条记录")

        except Exception as e:
            messagebox.showerror("错误", f"分析数据失败：{str(e)}")
            self.status_label.config(text="分析失败")

    def update_table(self):
        """更新表格显示"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 添加新数据（倒序显示，最新的在前面）
        for index in range(len(self.analyzed_data) - 1, -1, -1):
            row = self.analyzed_data.iloc[index]
            values = (
                row['开奖日期'].strftime('%Y-%m-%d'),
                row['期号'],
                row['红球'],
                row['蓝球'],
                row['奇偶比'],
                row['奇偶排布'],
                row['上次奇偶排布'],
                row['分区比'],
                row['上次分区比'],
                row['上次0分区比']
            )
            self.tree.insert('', 'end', values=values)

def main():
    """主函数"""
    root = tk.Tk()
    app = DaLeTouAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()