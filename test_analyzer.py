#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透分析器测试脚本
测试核心分析功能
"""

import pandas as pd
from dlt_analyzer import DaLeTouAnalyzer
import tkinter as tk

def test_calculations():
    """测试计算功能"""
    print("测试大乐透分析器核心功能...")

    # 创建一个临时的分析器实例
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    analyzer = DaLeTouAnalyzer(root)

    # 测试数据
    test_red_balls = [22, 24, 29, 31, 35]

    # 测试奇偶比计算
    odd_even_ratio = analyzer.calculate_odd_even_ratio(test_red_balls)
    print(f"红球 {test_red_balls} 的奇偶比: {odd_even_ratio}")

    # 测试奇偶排布计算
    odd_even_pattern = analyzer.calculate_odd_even_pattern(test_red_balls)
    print(f"红球 {test_red_balls} 的奇偶排布: {odd_even_pattern}")

    # 测试分区比计算
    zone_ratio = analyzer.calculate_zone_ratio(test_red_balls)
    print(f"红球 {test_red_balls} 的分区比: {zone_ratio}")

    # 测试更多样本
    test_cases = [
        [1, 3, 5, 7, 9],    # 全奇数
        [2, 4, 6, 8, 10],   # 全偶数
        [1, 2, 15, 16, 35], # 混合
        [5, 10, 15, 20, 25], # 每个区间一个
    ]

    print("\n更多测试样本:")
    for i, red_balls in enumerate(test_cases, 1):
        odd_even_ratio = analyzer.calculate_odd_even_ratio(red_balls)
        odd_even_pattern = analyzer.calculate_odd_even_pattern(red_balls)
        zone_ratio = analyzer.calculate_zone_ratio(red_balls)

        print(f"样本{i}: {red_balls}")
        print(f"  奇偶比: {odd_even_ratio}")
        print(f"  奇偶排布: {odd_even_pattern}")
        print(f"  分区比: {zone_ratio}")
        print()

    root.destroy()
    print("核心功能测试完成！")

def test_data_loading():
    """测试数据加载功能"""
    print("测试数据加载功能...")

    try:
        # 读取数据文件
        df = pd.read_csv("dlt_data.csv", encoding='utf-8')
        print(f"成功读取数据文件，共 {len(df)} 条记录")
        print(f"数据列: {list(df.columns)}")
        print(f"数据样本:")
        print(df.head())

        # 验证数据格式
        required_columns = ['期号', '开奖日期', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"警告：缺少列 {missing_columns}")
        else:
            print("数据格式验证通过！")

    except Exception as e:
        print(f"数据加载测试失败: {e}")

if __name__ == "__main__":
    test_calculations()
    print("-" * 50)
    test_data_loading()